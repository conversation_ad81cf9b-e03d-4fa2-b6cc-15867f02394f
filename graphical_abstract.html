<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graphical Abstract - Diagnostic Methods for Time-Varying Conditions</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .slide-container {
            width: 1200px;
            height: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
            box-sizing: border-box;
            position: relative;
        }

        .title {
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 30px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .subtitle {
            text-align: center;
            font-size: 18px;
            color: #34495e;
            margin-bottom: 40px;
            font-style: italic;
        }

        .workflow-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 20px 0;
        }

        .work-package {
            width: 280px;
            height: 180px;
            border-radius: 15px;
            padding: 20px;
            box-sizing: border-box;
            text-align: center;
            position: relative;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .work-package:hover {
            transform: translateY(-5px);
        }

        .wp1 {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 3px solid #1976d2;
        }

        .wp2 {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border: 3px solid #7b1fa2;
        }

        .wp3 {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
            border: 3px solid #e65100;
        }

        .wp-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            padding: 8px;
            border-radius: 8px;
            background: rgba(255,255,255,0.7);
        }

        .wp1 .wp-title { color: #1976d2; }
        .wp2 .wp-title { color: #7b1fa2; }
        .wp3 .wp-title { color: #e65100; }

        .wp-content {
            font-size: 14px;
            line-height: 1.6;
            color: #555;
            text-align: left;
        }

        .wp-tasks {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
            font-style: italic;
        }

        .arrow {
            font-size: 40px;
            color: #34495e;
            font-weight: bold;
            align-self: center;
        }

        .goals-section {
            margin: 20px 0;
        }

        .goals-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 25px;
        }

        .goals-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .goal-box {
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .goal-box:hover {
            transform: scale(1.05);
        }

        .g1 {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            border: 2px solid #4caf50;
        }
        .g2 {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border: 2px solid #9c27b0;
        }
        .g3 {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px solid #1976d2;
        }
        .g4 {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
            border: 2px solid #f57c00;
        }

        .goal-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .g1 .goal-number { color: #2e7d32; }
        .g2 .goal-number { color: #6a1b9a; }
        .g3 .goal-number { color: #1565c0; }
        .g4 .goal-number { color: #e65100; }

        .goal-text {
            font-size: 13px;
            line-height: 1.4;
            color: #555;
        }

        .innovation-box {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            border: 3px solid #ff8f00;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin: 30px 0;
            box-shadow: 0 6px 20px rgba(255,143,0,0.2);
        }

        .innovation-title {
            font-size: 20px;
            font-weight: bold;
            color: #e65100;
            margin-bottom: 15px;
        }

        .innovation-text {
            font-size: 16px;
            color: #bf360c;
            line-height: 1.5;
        }

        .methodology-flow {
            margin-top: 30px;
        }

        .flow-title {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .flow-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
        }

        .flow-step {
            flex: 1;
            padding: 15px 10px;
            border-radius: 10px;
            text-align: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .step1 { background: linear-gradient(135deg, #42a5f5 0%, #1976d2 100%); }
        .step2 { background: linear-gradient(135deg, #ab47bc 0%, #7b1fa2 100%); }
        .step3 { background: linear-gradient(135deg, #66bb6a 0%, #388e3c 100%); }
        .step4 { background: linear-gradient(135deg, #ffa726 0%, #f57c00 100%); }
        .step5 { background: linear-gradient(135deg, #ef5350 0%, #d32f2f 100%); }

        .flow-arrow {
            font-size: 20px;
            color: #666;
            margin: 0 5px;
        }

        .editable {
            cursor: text;
            outline: none;
            transition: background-color 0.3s ease;
        }

        .editable:hover {
            background-color: rgba(255,255,0,0.1);
        }

        .editable:focus {
            background-color: rgba(255,255,0,0.2);
            border-radius: 3px;
            padding: 2px;
        }

        @media print {
            body { background: white; }
            .slide-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="title editable" contenteditable="true">
            Diagnostic Methods for Machinery Under Time-Varying Operational Conditions
        </div>

        <div class="subtitle editable" contenteditable="true">
            Novel 2D/3D Signal Transformation Approaches for Robust Fault Detection
        </div>

        <div class="workflow-container">
            <div class="work-package wp1">
                <div class="wp-title editable" contenteditable="true">WP1: Data Acquisition</div>
                <div class="wp-content">
                    <div class="editable" contenteditable="true">• Acquisition of measurement data with time-varying speed and load conditions</div>
                    <div class="editable" contenteditable="true">• Validation of collected data</div>
                    <div class="editable" contenteditable="true">• Preprocessing of diagnostic data</div>
                </div>
                <div class="wp-tasks editable" contenteditable="true">Tasks: T1.1 - T1.3</div>
            </div>

            <div class="arrow">→</div>

            <div class="work-package wp2">
                <div class="wp-title editable" contenteditable="true">WP2: 2D/3D Transformations</div>
                <div class="wp-content">
                    <div class="editable" contenteditable="true">• Novel 2D/3D transforms for time-varying conditions</div>
                    <div class="editable" contenteditable="true">• Identification of assumptions and limitations</div>
                    <div class="editable" contenteditable="true">• Robust processing methods</div>
                </div>
                <div class="wp-tasks editable" contenteditable="true">Tasks: T2.1 - T2.2</div>
            </div>

            <div class="arrow">→</div>

            <div class="work-package wp3">
                <div class="wp-title editable" contenteditable="true">WP3: Diagnostics</div>
                <div class="wp-content">
                    <div class="editable" contenteditable="true">• Feature construction using ML methods</div>
                    <div class="editable" contenteditable="true">• Evaluation of multidimensional features</div>
                    <div class="editable" contenteditable="true">• Diagnostic criteria construction</div>
                </div>
                <div class="wp-tasks editable" contenteditable="true">Tasks: T3.1 - T3.4</div>
            </div>
        </div>

        <div class="goals-section">
            <div class="goals-title editable" contenteditable="true">Project Goals</div>
            <div class="goals-container">
                <div class="goal-box g1">
                    <div class="goal-number">G1</div>
                    <div class="goal-text editable" contenteditable="true">
                        Development of dedicated methodology of data validation and preprocessing for specific signals with time-varying operational conditions
                    </div>
                </div>

                <div class="goal-box g2">
                    <div class="goal-number">G2</div>
                    <div class="goal-text editable" contenteditable="true">
                        Development of novel 2D/3D representations adequate for time-varying operational conditions
                    </div>
                </div>

                <div class="goal-box g3">
                    <div class="goal-number">G3</div>
                    <div class="goal-text editable" contenteditable="true">
                        Development of diagnostic feature identification based on novel 2D/3D representations
                    </div>
                </div>

                <div class="goal-box g4">
                    <div class="goal-number">G4</div>
                    <div class="goal-text editable" contenteditable="true">
                        Development of diagnostic criteria adequate for time-varying operational conditions
                    </div>
                </div>
            </div>
        </div>

        <div class="innovation-box">
            <div class="innovation-title editable" contenteditable="true">Key Innovation</div>
            <div class="innovation-text editable" contenteditable="true">
                Robust diagnostic methods for machinery operating under time-varying speed and load conditions using novel 2D/3D signal transformation techniques for enhanced fault detection and condition monitoring
            </div>
        </div>

        <div class="methodology-flow">
            <div class="flow-title editable" contenteditable="true">Methodology Flow</div>
            <div class="flow-container">
                <div class="flow-step step1 editable" contenteditable="true">
                    Data Collection<br>& Validation<br><small>(Variable Conditions)</small>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step step2 editable" contenteditable="true">
                    Signal<br>Transformation<br><small>(2D/3D Maps)</small>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step step3 editable" contenteditable="true">
                    Feature<br>Extraction<br><small>(ML Methods)</small>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step step4 editable" contenteditable="true">
                    Diagnostic<br>Decision<br><small>(Health Index)</small>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step step5 editable" contenteditable="true">
                    Condition<br>Assessment<br><small>(Fault Detection)</small>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add some interactivity for better editing experience
        document.addEventListener('DOMContentLoaded', function() {
            const editableElements = document.querySelectorAll('.editable');

            editableElements.forEach(element => {
                element.addEventListener('focus', function() {
                    this.style.outline = '2px solid #007bff';
                });

                element.addEventListener('blur', function() {
                    this.style.outline = 'none';
                });

                // Prevent line breaks in single-line elements
                element.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !this.classList.contains('multi-line')) {
                        e.preventDefault();
                    }
                });
            });

            // Add print functionality
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'p') {
                    e.preventDefault();
                    window.print();
                }
            });
        });

        // Function to export content (you can expand this)
        function exportContent() {
            const content = document.querySelector('.slide-container').innerHTML;
            console.log('Slide content ready for export:', content);
            // You can add actual export functionality here
        }
    </script>
</body>
</html>