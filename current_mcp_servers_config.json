{"augment_code_mcp_servers": {"active_servers": [{"name": "filesystem", "functions": ["read_file_filesystem", "read_multiple_files_filesystem", "write_file_filesystem", "edit_file_filesystem", "create_directory_filesystem", "list_directory_filesystem", "directory_tree_filesystem", "move_file_filesystem", "search_files_filesystem", "get_file_info_filesystem", "list_allowed_directories_filesystem"], "description": "File system operations with directory access control", "allowed_directories": ["C:\\Users\\<USER>\\Desktop", "D:\\Drive\\Dropbox\\Python"]}, {"name": "context7", "functions": ["resolve-library-id_context7", "get-library-docs_context7"], "description": "Library documentation and code context retrieval"}, {"name": "memory", "functions": ["create_entities_memory", "create_relations_memory", "add_observations_memory", "delete_entities_memory", "delete_observations_memory", "delete_relations_memory", "read_graph_memory", "search_nodes_memory", "open_nodes_memory"], "description": "Knowledge graph and persistent memory operations"}, {"name": "sequential-thinking", "functions": ["sequentialthinking_sequentialthinking", "sequentialthinking_github_com_modelcontextprotocol_servers_tree_"], "description": "Advanced reasoning and problem-solving through structured thinking"}, {"name": "str-replace-editor", "functions": ["str-replace-editor"], "description": "Advanced code editing with precise line-based replacements"}, {"name": "browser-control", "functions": ["open-browser"], "description": "Web browser automation and control"}, {"name": "terminal-process", "functions": ["read-terminal", "launch-process", "kill-process", "read-process", "write-process", "list-processes"], "description": "Terminal and process management"}, {"name": "web-services", "functions": ["web-search", "web-fetch"], "description": "Web search and content fetching capabilities"}, {"name": "codebase-retrieval", "functions": ["codebase-retrieval"], "description": "Augment's proprietary codebase context engine"}, {"name": "file-management", "functions": ["remove-files", "save-file"], "description": "High-level file creation and deletion operations"}, {"name": "memory-simple", "functions": ["remember"], "description": "Simple memory storage for important information"}, {"name": "visualization", "functions": ["render-mermaid"], "description": "Diagram and visualization rendering"}, {"name": "ide-integration", "functions": ["diagnostics", "view"], "description": "IDE integration for diagnostics and file viewing"}], "workspace_info": {"root_directory": "d:\\Drive\\Dropbox\\Python\\ania_schemat", "repository_root": "d:\\Drive\\Dropbox\\Python\\ania_schemat", "current_working_directory": "d:\\Drive\\Dropbox\\Python\\ania_schemat", "os": "win32", "shell": "bash"}, "total_functions": 35, "server_count": 13}}